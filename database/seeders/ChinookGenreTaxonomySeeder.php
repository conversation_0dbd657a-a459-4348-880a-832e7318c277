<?php

namespace Database\Seeders;

use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ChinookGenreTaxonomySeeder extends Seeder
{
    /**
     * Original 25 Chinook genres with their IDs and names
     */
    private array $chinookGenres = [
        1 => 'Rock',
        2 => 'Jazz',
        3 => 'Metal',
        4 => 'Alternative & Punk',
        5 => 'Rock And Roll',
        6 => 'Blues',
        7 => 'Latin',
        8 => 'Reggae',
        9 => 'Pop',
        10 => 'Soundtrack',
        11 => 'Bossa Nova',
        12 => 'Easy Listening',
        13 => 'Heavy Metal',
        14 => 'R&B/Soul',
        15 => 'Electronica/Dance',
        16 => 'World',
        17 => 'Hip Hop/Rap',
        18 => 'Science Fiction',
        19 => 'TV Shows',
        20 => 'Sci Fi & Fantasy',
        21 => 'Drama',
        22 => 'Comedy',
        23 => 'Alternative',
        24 => 'Classical',
        25 => 'Opera',
    ];

    /**
     * Enhanced genre descriptions for better categorization
     */
    private array $genreDescriptions = [
        'Rock' => 'A broad genre of popular music that originated as "rock and roll" in the United States in the late 1940s and early 1950s.',
        'Jazz' => 'A music genre that originated in the African-American communities of New Orleans in the late 19th and early 20th centuries.',
        'Metal' => 'A genre of rock music that developed in the late 1960s and early 1970s, characterized by heavily amplified distortion and emphatic rhythms.',
        'Alternative & Punk' => 'Alternative rock and punk music that emerged from the independent music underground of the 1970s and 1980s.',
        'Rock And Roll' => 'The original form of rock music that emerged in the 1950s, combining elements of country, rhythm and blues, and pop.',
        'Blues' => 'A music genre and musical form which originated in the Deep South of the United States around the 1860s.',
        'Latin' => 'Music that originates from Latin America, Spain, and Portugal, encompassing various regional styles and rhythms.',
        'Reggae' => 'A music genre that originated in Jamaica in the late 1960s, characterized by a distinctive rhythm and social consciousness.',
        'Pop' => 'Popular music that originated in its modern form during the mid-1950s, characterized by catchy melodies and mass appeal.',
        'Soundtrack' => 'Music specifically composed or selected for films, television shows, video games, or other media.',
        'Bossa Nova' => 'A style of Brazilian music derived from samba and jazz, developed in the late 1950s and early 1960s.',
        'Easy Listening' => 'A popular music genre and radio format that emerged in the 1950s, characterized by relaxed tempos and mellow sounds.',
        'Heavy Metal' => 'A subgenre of metal music characterized by aggressive, driving rhythms and highly amplified distorted guitars.',
        'R&B/Soul' => 'Rhythm and blues and soul music that combines elements of gospel, blues, and jazz with a strong backbeat.',
        'Electronica/Dance' => 'Electronic music designed for dancing, encompassing various electronic dance music styles and subgenres.',
        'World' => 'Traditional and contemporary music from various cultures around the world, often featuring indigenous instruments.',
        'Hip Hop/Rap' => 'A cultural movement and music genre that originated in the Bronx, New York City, in the 1970s.',
        'Science Fiction' => 'Music and soundtracks specifically created for or associated with science fiction media and themes.',
        'TV Shows' => 'Music and soundtracks from television programs, including theme songs and background scores.',
        'Sci Fi & Fantasy' => 'Music associated with science fiction and fantasy genres, often featuring otherworldly or epic themes.',
        'Drama' => 'Music and soundtracks from dramatic productions, designed to enhance emotional storytelling.',
        'Comedy' => 'Music and soundtracks from comedic productions, often featuring light-hearted or humorous themes.',
        'Alternative' => 'Alternative music that exists outside of mainstream commercial music, often characterized by independence and innovation.',
        'Classical' => 'Art music produced in the traditions of Western culture, typically featuring orchestral instruments and formal structures.',
        'Opera' => 'A form of theatre in which music has a leading role, combining singing, orchestral music, and often dance.',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Chinook Genre Taxonomies...');

        DB::transaction(function () {
            foreach ($this->chinookGenres as $chinookId => $genreName) {
                $this->createGenreTaxonomy($chinookId, $genreName);
            }
        });

        $this->command->info('Successfully seeded ' . count($this->chinookGenres) . ' genre taxonomies.');
        $this->displayGenreStats();
    }

    /**
     * Create a genre taxonomy record
     */
    private function createGenreTaxonomy(int $chinookId, string $genreName): void
    {
        // Check if taxonomy already exists
        $existing = Taxonomy::where('type', 'genre')
            ->where('meta->chinook_id', $chinookId)
            ->first();

        if ($existing) {
            $this->command->warn("Genre taxonomy '{$genreName}' (Chinook ID: {$chinookId}) already exists. Skipping.");
            return;
        }

        // Create the taxonomy record
        $taxonomy = Taxonomy::create([
            'name' => $genreName,
            'slug' => Str::slug($genreName),
            'type' => 'genre',
            'description' => $this->genreDescriptions[$genreName] ?? "Genre imported from original Chinook database.",
            'parent_id' => null,
            'sort_order' => $chinookId,
            'meta' => [
                'chinook_id' => $chinookId,
                'source' => 'chinook_import',
                'import_date' => now()->toISOString(),
                'is_original_chinook' => true,
                'category_type' => 'GENRE',
            ],
        ]);

        $this->command->info("Created genre taxonomy: {$genreName} (ID: {$taxonomy->id}, Chinook ID: {$chinookId})");
    }

    /**
     * Display statistics about the seeded genres
     */
    private function displayGenreStats(): void
    {
        $totalGenres = Taxonomy::where('type', 'genre')->count();
        $chinookGenres = Taxonomy::where('type', 'genre')
            ->where('meta->is_original_chinook', true)
            ->count();

        $this->command->info("\n=== Genre Taxonomy Statistics ===");
        $this->command->info("Total genre taxonomies: {$totalGenres}");
        $this->command->info("Original Chinook genres: {$chinookGenres}");
        
        if ($chinookGenres === 25) {
            $this->command->info("✅ All 25 original Chinook genres successfully imported!");
        } else {
            $this->command->warn("⚠️  Expected 25 Chinook genres, found {$chinookGenres}");
        }

        // Display sample genres
        $this->command->info("\n=== Sample Genres ===");
        $sampleGenres = Taxonomy::where('type', 'genre')
            ->where('meta->is_original_chinook', true)
            ->orderBy('sort_order')
            ->limit(5)
            ->get(['name', 'slug', 'meta']);

        foreach ($sampleGenres as $genre) {
            $chinookId = $genre->meta['chinook_id'] ?? 'N/A';
            $this->command->info("- {$genre->name} (slug: {$genre->slug}, chinook_id: {$chinookId})");
        }
    }

    /**
     * Verify genre data integrity
     */
    public function verifyIntegrity(): bool
    {
        $this->command->info('Verifying genre taxonomy integrity...');

        $issues = [];

        // Check for missing genres
        foreach ($this->chinookGenres as $chinookId => $genreName) {
            $exists = Taxonomy::where('type', 'genre')
                ->where('meta->chinook_id', $chinookId)
                ->exists();

            if (!$exists) {
                $issues[] = "Missing genre: {$genreName} (Chinook ID: {$chinookId})";
            }
        }

        // Check for duplicate slugs
        $duplicateSlugs = Taxonomy::where('type', 'genre')
            ->select('slug')
            ->groupBy('slug')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('slug');

        foreach ($duplicateSlugs as $slug) {
            $issues[] = "Duplicate slug found: {$slug}";
        }

        // Check for orphaned taxonomies
        $orphanedCount = Taxonomy::where('type', 'genre')
            ->whereNull('meta->chinook_id')
            ->count();

        if ($orphanedCount > 0) {
            $issues[] = "Found {$orphanedCount} genre taxonomies without chinook_id";
        }

        if (empty($issues)) {
            $this->command->info('✅ Genre taxonomy integrity verified successfully!');
            return true;
        } else {
            $this->command->error('❌ Genre taxonomy integrity issues found:');
            foreach ($issues as $issue) {
                $this->command->error("  - {$issue}");
            }
            return false;
        }
    }
}
