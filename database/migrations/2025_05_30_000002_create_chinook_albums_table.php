<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chinook_albums', function (Blueprint $table) {
            $table->id();
            
            // Core fields
            $table->string('public_id', 50)->unique()->index();
            $table->string('title', 255)->index();
            $table->string('slug', 255)->unique()->index();
            
            // Relationships
            $table->foreignId('artist_id')
                  ->constrained('chinook_artists')
                  ->cascadeOnDelete();
            
            // Extended fields
            $table->date('release_date')->nullable()->index();
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->boolean('is_active')->default(true)->index();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // User stamps (for audit trail)
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            
            // Indexes for performance
            $table->index(['artist_id', 'is_active']);
            $table->index(['is_active', 'created_at']);
            $table->index(['title', 'is_active']);
            $table->index(['release_date', 'is_active']);
            $table->index('created_at');
            $table->index('updated_at');
            
            // Composite indexes for common queries
            $table->index(['artist_id', 'release_date']);
            $table->index(['artist_id', 'title']);
            
            // Full-text search index for title and description
            $table->fullText(['title', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chinook_albums');
    }
};
