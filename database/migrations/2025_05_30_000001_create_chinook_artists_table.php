<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chinook_artists', function (Blueprint $table) {
            $table->id();
            
            // Core fields
            $table->string('public_id', 50)->unique()->index();
            $table->string('name', 255)->index();
            $table->string('slug', 255)->unique()->index();
            
            // Extended fields
            $table->text('biography')->nullable();
            $table->json('metadata')->nullable();
            $table->boolean('is_active')->default(true)->index();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // User stamps (for audit trail)
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            
            // Indexes for performance
            $table->index(['is_active', 'created_at']);
            $table->index(['name', 'is_active']);
            $table->index('created_at');
            $table->index('updated_at');
            
            // Full-text search index for name and biography
            $table->fullText(['name', 'biography']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chinook_artists');
    }
};
