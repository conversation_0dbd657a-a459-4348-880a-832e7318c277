<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create a view that maps taxonomy records to the original Chinook genres format
        // This provides backward compatibility with the original schema
        DB::statement("
            CREATE VIEW chinook_genres AS
            SELECT 
                t.id,
                t.name,
                t.slug,
                t.description,
                t.created_at,
                t.updated_at,
                JSON_UNQUOTE(JSON_EXTRACT(t.meta, '$.chinook_id')) as chinook_id
            FROM taxonomies t
            WHERE t.type = 'genre'
            AND t.deleted_at IS NULL
            ORDER BY t.name
        ");

        // Create indexes on the underlying taxonomy table for genre queries
        Schema::table('taxonomies', function (Blueprint $table) {
            // Composite index for genre-specific queries
            $table->index(['type', 'name'], 'idx_taxonomies_genre_name');
            $table->index(['type', 'slug'], 'idx_taxonomies_genre_slug');
            $table->index(['type', 'created_at'], 'idx_taxonomies_genre_created');
            
            // JSON index for chinook_id lookups (MySQL 5.7+)
            if (DB::getDriverName() === 'mysql') {
                DB::statement("ALTER TABLE taxonomies ADD INDEX idx_taxonomies_chinook_id ((CAST(JSON_UNQUOTE(JSON_EXTRACT(meta, '$.chinook_id')) AS UNSIGNED)))");
            }
        });

        // Create indexes on taxonomables for genre relationship queries
        Schema::table('taxonomables', function (Blueprint $table) {
            // Composite indexes for polymorphic relationships with genres
            $table->index(['taxonomy_id', 'taxonomable_type'], 'idx_taxonomables_genre_type');
            $table->index(['taxonomable_id', 'taxonomable_type', 'taxonomy_id'], 'idx_taxonomables_poly_full');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the view
        DB::statement("DROP VIEW IF EXISTS chinook_genres");

        // Drop the custom indexes
        Schema::table('taxonomies', function (Blueprint $table) {
            $table->dropIndex('idx_taxonomies_genre_name');
            $table->dropIndex('idx_taxonomies_genre_slug');
            $table->dropIndex('idx_taxonomies_genre_created');
        });

        // Drop JSON index for MySQL
        if (DB::getDriverName() === 'mysql') {
            try {
                DB::statement("ALTER TABLE taxonomies DROP INDEX idx_taxonomies_chinook_id");
            } catch (Exception $e) {
                // Index might not exist, ignore error
            }
        }

        Schema::table('taxonomables', function (Blueprint $table) {
            $table->dropIndex('idx_taxonomables_genre_type');
            $table->dropIndex('idx_taxonomables_poly_full');
        });
    }
};
