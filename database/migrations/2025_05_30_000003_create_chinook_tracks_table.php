<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chinook_tracks', function (Blueprint $table) {
            $table->id();
            
            // Core fields
            $table->string('public_id', 50)->unique()->index();
            $table->string('name', 255)->index();
            $table->string('slug', 255)->unique()->index();
            
            // Relationships
            $table->foreignId('album_id')
                  ->constrained('chinook_albums')
                  ->cascadeOnDelete();
            
            // Media information
            $table->unsignedBigInteger('media_type_id')->nullable()->index();
            $table->string('composer', 255)->nullable()->index();
            $table->unsignedInteger('milliseconds')->index();
            $table->unsignedBigInteger('bytes')->nullable();
            $table->decimal('unit_price', 10, 2)->default(0.99)->index();
            
            // Extended fields
            $table->json('metadata')->nullable();
            $table->boolean('is_active')->default(true)->index();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // User stamps (for audit trail)
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            
            // Indexes for performance
            $table->index(['album_id', 'is_active']);
            $table->index(['is_active', 'created_at']);
            $table->index(['name', 'is_active']);
            $table->index(['milliseconds', 'is_active']);
            $table->index(['unit_price', 'is_active']);
            $table->index(['composer', 'is_active']);
            $table->index('created_at');
            $table->index('updated_at');
            
            // Composite indexes for common queries
            $table->index(['album_id', 'milliseconds']);
            $table->index(['album_id', 'name']);
            $table->index(['composer', 'album_id']);
            $table->index(['unit_price', 'milliseconds']);
            
            // Duration-based indexes for filtering
            $table->index(['milliseconds', 'unit_price']);
            
            // Full-text search index for name and composer
            $table->fullText(['name', 'composer']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chinook_tracks');
    }
};
