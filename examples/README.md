# Chinook Taxonomy Implementation Examples

This directory contains practical examples demonstrating the single taxonomy system implementation for the Chinook music database using Laravel 12 and the `aliziodev/laravel-taxonomy` package.

## Overview

The examples showcase:

- **Greenfield Implementation**: Clean, modern Laravel 12 architecture
- **Single Taxonomy System**: Using only `aliziodev/laravel-taxonomy` package
- **Compatibility Layer**: Backward compatibility with original Chinook schema
- **Modern Patterns**: Laravel 12 syntax and best practices
- **Performance Optimization**: Efficient queries and bulk operations

## Running the Examples

### Prerequisites

1. **Database Setup**: Ensure migrations are run and taxonomy system is seeded
   ```bash
   php artisan migrate
   php artisan db:seed --class=ChinookGenreTaxonomySeeder
   ```

2. **Verify Installation**: Check that the taxonomy package is properly configured
   ```bash
   php artisan tinker
   >>> Aliziodev\LaravelTaxonomy\Models\Taxonomy::where('type', 'genre')->count()
   => 25
   ```

### Running Examples

#### Option 1: Interactive Tinker Session

```bash
php artisan tinker
>>> $examples = new Examples\ChinookTaxonomyExamples();
>>> $examples->runAllExamples();
```

#### Option 2: Individual Examples

```bash
php artisan tinker
>>> $examples = new Examples\ChinookTaxonomyExamples();
>>> $examples->createArtistWithGenres();
>>> $examples->demonstrateCompatibilityLayer();
>>> $examples->queryWithTaxonomyFilters();
```

#### Option 3: Custom Artisan Command (Optional)

Create a custom command to run examples:

```bash
php artisan make:command RunChinookExamples
```

## Example Categories

### 1. Basic CRUD Operations

- **Artist Creation**: Creating artists with genre classification
- **Album Management**: Albums with inherited genres from artists
- **Track Handling**: Tracks with multiple taxonomy types (genre, mood, instrument)

### 2. Taxonomy Integration

- **Genre Assignment**: Primary and secondary genre classification
- **Multiple Types**: Using different taxonomy types (genre, mood, era, style)
- **Metadata Storage**: Storing additional context in pivot tables

### 3. Querying and Filtering

- **Genre Queries**: Finding content by specific genres
- **Complex Filters**: Multi-criteria searches with taxonomy filters
- **Performance**: Efficient queries with eager loading

### 4. Compatibility Layer

- **Original Format**: Import/export in original Chinook format
- **ID Mapping**: Maintaining original Chinook ID references
- **Statistics**: Genre usage statistics and analytics

### 5. Advanced Features

- **Hierarchical Genres**: Parent-child genre relationships
- **Bulk Operations**: Efficient mass data operations
- **Data Migration**: Importing from original Chinook database

## Key Features Demonstrated

### Laravel 12 Modern Syntax

```php
// Modern cast() method
protected function casts(): array
{
    return [
        'metadata' => 'array',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
    ];
}
```

### Single Taxonomy System

```php
// Only using aliziodev/laravel-taxonomy
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;

class ChinookArtist extends Model
{
    use HasTaxonomies;  // Single taxonomy system
    
    public function getTaxonomyTypes(): array
    {
        return ['genre', 'style', 'era', 'origin'];
    }
}
```

### Compatibility Layer

```php
// Backward compatibility with original schema
$rockGenre = ChinookGenre::findByChinookId(1);
$chinookData = ChinookGenre::exportToChinookFormat();
ChinookGenre::importFromChinookData($originalData);
```

### Performance Optimization

```php
// Efficient eager loading
$artists = ChinookArtist::with([
    'taxonomies' => fn($q) => $q->where('type', 'genre')
])->get();

// Bulk operations
foreach ($artists as $artist) {
    $artist->attachGenre($genreId, true, ['bulk' => true]);
}
```

## Testing the Examples

### Unit Tests

Run the comprehensive test suite to verify functionality:

```bash
php artisan test --filter=ChinookTaxonomy
```

### Integration Tests

Test the complete workflow:

```bash
php artisan test tests/Feature/ChinookTaxonomyIntegrationTest.php
```

### Compatibility Tests

Verify backward compatibility:

```bash
php artisan test tests/Unit/ChinookGenreCompatibilityTest.php
```

## Performance Considerations

### Database Optimization

- **Indexes**: Proper indexing on taxonomy relationships
- **Eager Loading**: Prevent N+1 query problems
- **Bulk Operations**: Efficient mass data operations

### Query Optimization

```php
// Good: Eager load relationships
$tracks = ChinookTrack::with(['album.artist', 'taxonomies'])->get();

// Bad: N+1 queries
$tracks = ChinookTrack::all();
foreach ($tracks as $track) {
    echo $track->album->artist->name; // N+1 problem
}
```

### Memory Management

```php
// Process large datasets in chunks
ChinookTrack::chunk(1000, function ($tracks) {
    foreach ($tracks as $track) {
        // Process track
    }
});
```

## Troubleshooting

### Common Issues

1. **Taxonomy Not Found**: Ensure seeder has run
   ```bash
   php artisan db:seed --class=ChinookGenreTaxonomySeeder
   ```

2. **Relationship Errors**: Check foreign key constraints
   ```php
   // Verify relationships exist
   $artist = ChinookArtist::with('albums.tracks')->find(1);
   ```

3. **Performance Issues**: Use proper eager loading
   ```php
   // Enable query logging to debug
   DB::enableQueryLog();
   // Your queries here
   dd(DB::getQueryLog());
   ```

### Debugging Tips

- **Query Logging**: Monitor SQL queries for optimization
- **Model Events**: Use model events for debugging relationships
- **Taxonomy Validation**: Verify taxonomy types and relationships

## Best Practices

### Model Design

- **Consistent Traits**: Use same traits across all models
- **Type Safety**: Proper type hints and return types
- **Modern Syntax**: Laravel 12 patterns throughout

### Taxonomy Usage

- **Predefined Types**: Use consistent taxonomy types
- **Metadata**: Store context in pivot metadata
- **Performance**: Optimize queries with proper indexing

### Data Integrity

- **Validation**: Validate taxonomy assignments
- **Constraints**: Use foreign key constraints
- **Transactions**: Wrap complex operations in transactions

## Next Steps

1. **Extend Examples**: Add more complex scenarios
2. **Performance Testing**: Benchmark with large datasets
3. **API Integration**: Create REST API endpoints
4. **Frontend Integration**: Connect with Livewire/Vue components
5. **Analytics**: Build reporting and analytics features

## Resources

- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [aliziodev/laravel-taxonomy Package](https://github.com/aliziodev/laravel-taxonomy)
- [Chinook Database Documentation](docs/chinook-taxonomy-implementation.md)
- [Testing Guide](tests/README.md)
