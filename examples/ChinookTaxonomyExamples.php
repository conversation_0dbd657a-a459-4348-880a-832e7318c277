<?php

/**
 * Chinook Taxonomy Implementation Examples
 * 
 * This file demonstrates practical usage of the single taxonomy system
 * implementation with Laravel 12 modern patterns.
 */

namespace Examples;

use App\Models\ChinookArtist;
use App\Models\ChinookAlbum;
use App\Models\ChinookTrack;
use App\Models\ChinookGenre;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Support\Facades\DB;

class ChinookTaxonomyExamples
{
    /**
     * Example 1: Creating Artists with Genre Classification
     */
    public function createArtistWithGenres(): void
    {
        // Create a new artist
        $artist = ChinookArtist::create([
            'name' => 'Pink Floyd',
            'biography' => 'English rock band formed in London in 1965, known for progressive and psychedelic music.',
        ]);

        // Find relevant genres
        $rockGenre = Taxonomy::where('type', 'genre')->where('name', 'Rock')->first();
        $alternativeGenre = Taxonomy::where('type', 'genre')->where('name', 'Alternative')->first();

        // Attach primary genre
        $artist->attachGenre($rockGenre->id, true, [
            'confidence' => 0.95,
            'source' => 'manual_classification',
        ]);

        // Attach secondary genre
        $artist->attachGenre($alternativeGenre->id, false, [
            'confidence' => 0.80,
            'source' => 'manual_classification',
        ]);

        echo "Created artist: {$artist->name} with genres: {$artist->genres->pluck('name')->implode(', ')}\n";
    }

    /**
     * Example 2: Album Creation with Genre Inheritance
     */
    public function createAlbumWithInheritedGenres(): void
    {
        // Find or create artist
        $artist = ChinookArtist::where('name', 'The Beatles')->first();
        if (!$artist) {
            $artist = ChinookArtist::create([
                'name' => 'The Beatles',
                'biography' => 'English rock band formed in Liverpool in 1960.',
            ]);

            // Attach genres to artist
            $rockGenre = Taxonomy::where('type', 'genre')->where('name', 'Rock')->first();
            $popGenre = Taxonomy::where('type', 'genre')->where('name', 'Pop')->first();
            
            $artist->attachGenre($rockGenre->id, true);
            $artist->attachGenre($popGenre->id, false);
        }

        // Create album
        $album = ChinookAlbum::create([
            'title' => 'Abbey Road',
            'artist_id' => $artist->id,
            'release_date' => '1969-09-26',
            'description' => 'The eleventh studio album by the English rock band The Beatles.',
        ]);

        // Genres are automatically inherited from artist
        $album->inheritArtistGenres();

        echo "Created album: {$album->title} with inherited genres: {$album->genres->pluck('name')->implode(', ')}\n";
    }

    /**
     * Example 3: Track Creation with Multiple Taxonomy Types
     */
    public function createTrackWithMultipleTaxonomies(): void
    {
        // Find album
        $album = ChinookAlbum::where('title', 'Abbey Road')->first();
        
        if (!$album) {
            throw new \Exception('Album not found. Run createAlbumWithInheritedGenres() first.');
        }

        // Create track
        $track = ChinookTrack::create([
            'name' => 'Come Together',
            'album_id' => $album->id,
            'composer' => 'Lennon-McCartney',
            'milliseconds' => 259000, // 4:19
            'unit_price' => 0.99,
        ]);

        // Inherit genres from album
        $track->inheritAlbumGenres();

        // Add mood taxonomies (create if needed)
        $moodTaxonomies = [
            ['name' => 'Groovy', 'type' => 'mood'],
            ['name' => 'Mysterious', 'type' => 'mood'],
        ];

        foreach ($moodTaxonomies as $moodData) {
            $mood = Taxonomy::firstOrCreate(
                ['name' => $moodData['name'], 'type' => $moodData['type']],
                ['slug' => \Str::slug($moodData['name']), 'description' => "Mood: {$moodData['name']}"]
            );

            $track->attachTaxonomy($mood->id, [
                'confidence' => 0.85,
                'source' => 'manual_classification',
            ]);
        }

        echo "Created track: {$track->name} with taxonomies: ";
        echo $track->taxonomies->groupBy('type')->map(fn($group, $type) => 
            "{$type}: " . $group->pluck('name')->implode(', ')
        )->implode(' | ') . "\n";
    }

    /**
     * Example 4: Querying with Taxonomy Filters
     */
    public function queryWithTaxonomyFilters(): void
    {
        echo "=== Taxonomy Query Examples ===\n";

        // Find all rock artists
        $rockArtists = ChinookArtist::byGenre('rock')->get();
        echo "Rock artists: {$rockArtists->count()}\n";

        // Find all albums from the 1960s (if era taxonomy exists)
        $sixties = Taxonomy::where('type', 'era')->where('name', '1960s')->first();
        if ($sixties) {
            $sixtiesAlbums = ChinookAlbum::whereHas('taxonomies', function ($q) use ($sixties) {
                $q->where('taxonomy_id', $sixties->id);
            })->get();
            echo "1960s albums: {$sixtiesAlbums->count()}\n";
        }

        // Find tracks with specific mood
        $groovyTracks = ChinookTrack::whereHas('taxonomies', function ($q) {
            $q->where('type', 'mood')->where('name', 'Groovy');
        })->get();
        echo "Groovy tracks: {$groovyTracks->count()}\n";

        // Complex query: Rock tracks longer than 4 minutes
        $longRockTracks = ChinookTrack::byGenre('rock')
            ->byDuration(240000) // 4 minutes in milliseconds
            ->get();
        echo "Long rock tracks: {$longRockTracks->count()}\n";
    }

    /**
     * Example 5: Compatibility Layer Usage
     */
    public function demonstrateCompatibilityLayer(): void
    {
        echo "=== Compatibility Layer Examples ===\n";

        // Find genre by original Chinook ID
        $rockGenre = ChinookGenre::findByChinookId(1);
        if ($rockGenre) {
            echo "Found Rock genre by Chinook ID: {$rockGenre->name}\n";
            
            // Get statistics
            $stats = $rockGenre->getStats();
            echo "Rock genre stats: {$stats['artists_count']} artists, {$stats['albums_count']} albums, {$stats['tracks_count']} tracks\n";
        }

        // Export all genres to Chinook format
        $chinookData = ChinookGenre::exportToChinookFormat();
        echo "Exported {" . count($chinookData) . "} genres to Chinook format\n";

        // Show sample export data
        echo "Sample export data:\n";
        foreach (array_slice($chinookData, 0, 3) as $genre) {
            echo "  ID: {$genre['id']}, Name: {$genre['name']}\n";
        }
    }

    /**
     * Example 6: Advanced Taxonomy Relationships
     */
    public function demonstrateAdvancedRelationships(): void
    {
        echo "=== Advanced Relationship Examples ===\n";

        // Create hierarchical genre taxonomy (if needed)
        $metalGenre = Taxonomy::firstOrCreate(
            ['name' => 'Metal', 'type' => 'genre'],
            ['slug' => 'metal', 'description' => 'Metal music genre']
        );

        $heavyMetalGenre = Taxonomy::firstOrCreate(
            ['name' => 'Heavy Metal', 'type' => 'genre', 'parent_id' => $metalGenre->id],
            ['slug' => 'heavy-metal', 'description' => 'Heavy Metal subgenre']
        );

        echo "Created hierarchical genres: {$metalGenre->name} -> {$heavyMetalGenre->name}\n";

        // Find all metal subgenres
        $metalSubgenres = Taxonomy::where('type', 'genre')
            ->where('parent_id', $metalGenre->id)
            ->get();
        echo "Metal subgenres: {$metalSubgenres->pluck('name')->implode(', ')}\n";

        // Create artist with subgenre
        $metalArtist = ChinookArtist::create([
            'name' => 'Black Sabbath',
            'biography' => 'English heavy metal band formed in Birmingham in 1968.',
        ]);

        $metalArtist->attachGenre($heavyMetalGenre->id, true, [
            'subgenre_specificity' => 'high',
            'influence_level' => 'pioneering',
        ]);

        echo "Created metal artist: {$metalArtist->name} with subgenre: {$metalArtist->primaryGenre()->name}\n";
    }

    /**
     * Example 7: Bulk Operations and Performance
     */
    public function demonstrateBulkOperations(): void
    {
        echo "=== Bulk Operations Examples ===\n";

        // Bulk attach genres to multiple artists
        $artists = ChinookArtist::limit(5)->get();
        $rockGenre = Taxonomy::where('type', 'genre')->where('name', 'Rock')->first();

        if ($rockGenre) {
            foreach ($artists as $artist) {
                if ($artist->genres()->count() === 0) {
                    $artist->attachGenre($rockGenre->id, true, [
                        'bulk_operation' => true,
                        'assigned_at' => now(),
                    ]);
                }
            }
            echo "Bulk assigned Rock genre to {$artists->count()} artists\n";
        }

        // Efficient query with eager loading
        $artistsWithGenres = ChinookArtist::with([
            'taxonomies' => function ($query) {
                $query->where('type', 'genre');
            }
        ])->limit(10)->get();

        echo "Loaded {$artistsWithGenres->count()} artists with genres (eager loaded)\n";

        // Show query count
        DB::enableQueryLog();
        foreach ($artistsWithGenres as $artist) {
            $genreNames = $artist->taxonomies->pluck('name')->implode(', ');
            // This doesn't trigger additional queries due to eager loading
        }
        $queryCount = count(DB::getQueryLog());
        DB::disableQueryLog();
        
        echo "Total queries executed: {$queryCount}\n";
    }

    /**
     * Example 8: Data Import from Original Chinook
     */
    public function demonstrateDataImport(): void
    {
        echo "=== Data Import Examples ===\n";

        // Simulate original Chinook data
        $originalGenres = [
            ['id' => 1, 'name' => 'Rock'],
            ['id' => 2, 'name' => 'Jazz'],
            ['id' => 3, 'name' => 'Metal'],
        ];

        // Import using compatibility layer
        ChinookGenre::importFromChinookData($originalGenres);
        echo "Imported {" . count($originalGenres) . "} genres from original Chinook data\n";

        // Verify import
        foreach ($originalGenres as $genreData) {
            $imported = ChinookGenre::findByChinookId($genreData['id']);
            if ($imported) {
                echo "✓ Verified import: {$imported->name} (Chinook ID: {$genreData['id']})\n";
            } else {
                echo "✗ Import failed: {$genreData['name']}\n";
            }
        }
    }

    /**
     * Run all examples
     */
    public function runAllExamples(): void
    {
        echo "Running Chinook Taxonomy Implementation Examples...\n\n";

        try {
            $this->createArtistWithGenres();
            echo "\n";

            $this->createAlbumWithInheritedGenres();
            echo "\n";

            $this->createTrackWithMultipleTaxonomies();
            echo "\n";

            $this->queryWithTaxonomyFilters();
            echo "\n";

            $this->demonstrateCompatibilityLayer();
            echo "\n";

            $this->demonstrateAdvancedRelationships();
            echo "\n";

            $this->demonstrateBulkOperations();
            echo "\n";

            $this->demonstrateDataImport();
            echo "\n";

            echo "All examples completed successfully!\n";

        } catch (\Exception $e) {
            echo "Error running examples: {$e->getMessage()}\n";
            echo "Stack trace:\n{$e->getTraceAsString()}\n";
        }
    }
}
