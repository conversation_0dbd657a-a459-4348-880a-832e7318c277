<?php

use App\Models\ChinookGenre;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ChinookGenre Compatibility Layer', function () {
    beforeEach(function () {
        // Create sample taxonomy records for testing
        $this->rockTaxonomy = Taxonomy::create([
            'name' => 'Rock',
            'slug' => 'rock',
            'type' => 'genre',
            'description' => 'Rock music genre',
            'meta' => [
                'chinook_id' => 1,
                'source' => 'chinook_import',
                'is_original_chinook' => true,
            ],
        ]);

        $this->jazzTaxonomy = Taxonomy::create([
            'name' => 'Jazz',
            'slug' => 'jazz',
            'type' => 'genre',
            'description' => 'Jazz music genre',
            'meta' => [
                'chinook_id' => 2,
                'source' => 'chinook_import',
                'is_original_chinook' => true,
            ],
        ]);
    });

    describe('Factory Methods', function () {
        it('creates ChinookGenre from taxonomy', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);

            expect($genre->id)->toBe($this->rockTaxonomy->id);
            expect($genre->name)->toBe('Rock');
            expect($genre->slug)->toBe('rock');
            expect($genre->description)->toBe('Rock music genre');
        });

        it('throws exception when creating from non-genre taxonomy', function () {
            $nonGenreTaxonomy = Taxonomy::create([
                'name' => 'Happy',
                'slug' => 'happy',
                'type' => 'mood',
                'description' => 'Happy mood',
            ]);

            expect(fn() => ChinookGenre::createFromTaxonomy($nonGenreTaxonomy))
                ->toThrow(InvalidArgumentException::class, 'Taxonomy must be of type "genre"');
        });

        it('finds genre by Chinook ID', function () {
            $genre = ChinookGenre::findByChinookId(1);

            expect($genre)->not->toBeNull();
            expect($genre->name)->toBe('Rock');
        });

        it('returns null for non-existent Chinook ID', function () {
            $genre = ChinookGenre::findByChinookId(999);

            expect($genre)->toBeNull();
        });

        it('finds genre by name case-insensitively', function () {
            $genre = ChinookGenre::findByName('ROCK');

            expect($genre)->not->toBeNull();
            expect($genre->name)->toBe('Rock');
        });

        it('returns null for non-existent genre name', function () {
            $genre = ChinookGenre::findByName('NonExistent');

            expect($genre)->toBeNull();
        });
    });

    describe('Data Export/Import', function () {
        it('exports to Chinook array format', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);
            $chinookArray = $genre->toChinookArray();

            expect($chinookArray)->toHaveKeys(['id', 'name']);
            expect($chinookArray['id'])->toBe(1);
            expect($chinookArray['name'])->toBe('Rock');
        });

        it('exports all genres to Chinook format', function () {
            $chinookData = ChinookGenre::exportToChinookFormat();

            expect($chinookData)->toHaveCount(2);
            expect($chinookData[0])->toHaveKeys(['id', 'name']);
            expect($chinookData[1])->toHaveKeys(['id', 'name']);
        });

        it('imports from Chinook data format', function () {
            // Clear existing data
            Taxonomy::where('type', 'genre')->delete();

            $chinookData = [
                ['id' => 1, 'name' => 'Rock'],
                ['id' => 2, 'name' => 'Jazz'],
                ['id' => 3, 'name' => 'Pop'],
            ];

            ChinookGenre::importFromChinookData($chinookData);

            $genreCount = Taxonomy::where('type', 'genre')->count();
            expect($genreCount)->toBe(3);

            $rockGenre = ChinookGenre::findByChinookId(1);
            expect($rockGenre->name)->toBe('Rock');
        });

        it('skips existing genres during import', function () {
            $chinookData = [
                ['id' => 1, 'name' => 'Rock'], // Already exists
                ['id' => 3, 'name' => 'Pop'],  // New genre
            ];

            ChinookGenre::importFromChinookData($chinookData);

            $genreCount = Taxonomy::where('type', 'genre')->count();
            expect($genreCount)->toBe(3); // 2 existing + 1 new
        });
    });

    describe('Genre Creation', function () {
        it('creates genre in taxonomy system', function () {
            $genre = ChinookGenre::createGenre([
                'name' => 'Electronic',
                'description' => 'Electronic music genre',
                'chinook_id' => 15,
            ]);

            expect($genre->name)->toBe('Electronic');
            expect($genre->slug)->toBe('electronic');

            // Verify underlying taxonomy was created
            $taxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Electronic')
                ->first();

            expect($taxonomy)->not->toBeNull();
            expect($taxonomy->meta['chinook_id'])->toBe(15);
        });

        it('auto-generates slug when not provided', function () {
            $genre = ChinookGenre::createGenre([
                'name' => 'Hip Hop/Rap',
                'chinook_id' => 17,
            ]);

            expect($genre->slug)->toBe('hip-hop-rap');
        });
    });

    describe('Relationship Queries', function () {
        it('provides taxonomy relationship', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);
            $taxonomy = $genre->taxonomy;

            expect($taxonomy)->not->toBeNull();
            expect($taxonomy->id)->toBe($this->rockTaxonomy->id);
            expect($taxonomy->type)->toBe('genre');
        });

        it('syncs with underlying taxonomy', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);

            // Update the underlying taxonomy
            $this->rockTaxonomy->update([
                'name' => 'Rock Music',
                'description' => 'Updated rock description',
            ]);

            // Sync the genre
            $genre->syncWithTaxonomy();

            expect($genre->name)->toBe('Rock Music');
            expect($genre->description)->toBe('Updated rock description');
        });
    });

    describe('Statistics and Utilities', function () {
        it('provides empty stats for new genre', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);
            $stats = $genre->getStats();

            expect($stats)->toHaveKeys(['artists_count', 'albums_count', 'tracks_count']);
            expect($stats['artists_count'])->toBe(0);
            expect($stats['albums_count'])->toBe(0);
            expect($stats['tracks_count'])->toBe(0);
        });

        it('retrieves all genres', function () {
            $allGenres = ChinookGenre::allGenres();

            expect($allGenres)->toHaveCount(2);
            expect($allGenres->pluck('name')->toArray())->toContain('Rock', 'Jazz');
        });
    });

    describe('Database Operation Prevention', function () {
        it('prevents direct creation', function () {
            expect(fn() => ChinookGenre::create(['name' => 'Test']))
                ->toThrow(Exception::class, 'ChinookGenre is a compatibility layer');
        });

        it('prevents direct updates', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);
            
            expect(fn() => $genre->update(['name' => 'Updated']))
                ->toThrow(Exception::class, 'ChinookGenre is a compatibility layer');
        });

        it('prevents direct deletion', function () {
            $genre = ChinookGenre::createFromTaxonomy($this->rockTaxonomy);
            
            expect(fn() => $genre->delete())
                ->toThrow(Exception::class, 'ChinookGenre is a compatibility layer');
        });
    });

    describe('Edge Cases', function () {
        it('handles genres without chinook_id in export', function () {
            $customTaxonomy = Taxonomy::create([
                'name' => 'Custom Genre',
                'slug' => 'custom-genre',
                'type' => 'genre',
                'description' => 'A custom genre without chinook_id',
                'meta' => [
                    'source' => 'manual_creation',
                ],
            ]);

            $genre = ChinookGenre::createFromTaxonomy($customTaxonomy);
            $chinookArray = $genre->toChinookArray();

            expect($chinookArray['id'])->toBe($customTaxonomy->id);
            expect($chinookArray['name'])->toBe('Custom Genre');
        });

        it('handles special characters in genre names', function () {
            $specialTaxonomy = Taxonomy::create([
                'name' => 'R&B/Soul',
                'slug' => 'rb-soul',
                'type' => 'genre',
                'description' => 'Rhythm and Blues/Soul music',
                'meta' => [
                    'chinook_id' => 14,
                    'source' => 'chinook_import',
                ],
            ]);

            $genre = ChinookGenre::createFromTaxonomy($specialTaxonomy);

            expect($genre->name)->toBe('R&B/Soul');
            expect($genre->slug)->toBe('rb-soul');
        });
    });
});
