<?php

use App\Models\ChinookArtist;
use App\Models\ChinookAlbum;
use App\Models\ChinookTrack;
use App\Models\ChinookGenre;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Chinook Taxonomy Integration', function () {
    beforeEach(function () {
        // Seed the taxonomy system with genres
        $this->artisan('db:seed', ['--class' => 'ChinookGenreTaxonomySeeder']);
    });

    describe('Genre Taxonomy Seeding', function () {
        it('creates all 25 original Chinook genres', function () {
            $genreCount = Taxonomy::where('type', 'genre')->count();
            expect($genreCount)->toBe(25);
        });

        it('creates genres with proper metadata', function () {
            $rockGenre = Taxonomy::where('type', 'genre')
                ->where('name', 'Rock')
                ->first();

            expect($rockGenre)->not->toBeNull();
            expect($rockGenre->meta['chinook_id'])->toBe(1);
            expect($rockGenre->meta['source'])->toBe('chinook_import');
            expect($rockGenre->meta['is_original_chinook'])->toBeTrue();
        });

        it('creates unique slugs for all genres', function () {
            $slugs = Taxonomy::where('type', 'genre')->pluck('slug')->toArray();
            $uniqueSlugs = array_unique($slugs);
            
            expect(count($slugs))->toBe(count($uniqueSlugs));
        });

        it('handles special characters in genre names correctly', function () {
            $alternativePunk = Taxonomy::where('type', 'genre')
                ->where('name', 'Alternative & Punk')
                ->first();

            expect($alternativePunk)->not->toBeNull();
            expect($alternativePunk->slug)->toBe('alternative-punk');
        });
    });

    describe('ChinookGenre Compatibility Layer', function () {
        it('can retrieve all genres through compatibility layer', function () {
            $genres = ChinookGenre::allGenres();
            expect($genres)->toHaveCount(25);
        });

        it('can find genre by original Chinook ID', function () {
            $rockGenre = ChinookGenre::findByChinookId(1);
            
            expect($rockGenre)->not->toBeNull();
            expect($rockGenre->name)->toBe('Rock');
        });

        it('can find genre by name case-insensitively', function () {
            $jazzGenre = ChinookGenre::findByName('JAZZ');
            
            expect($jazzGenre)->not->toBeNull();
            expect($jazzGenre->name)->toBe('Jazz');
        });

        it('exports to Chinook format correctly', function () {
            $rockGenre = ChinookGenre::findByChinookId(1);
            $chinookArray = $rockGenre->toChinookArray();
            
            expect($chinookArray)->toHaveKeys(['id', 'name']);
            expect($chinookArray['id'])->toBe(1);
            expect($chinookArray['name'])->toBe('Rock');
        });

        it('prevents direct database operations', function () {
            expect(fn() => ChinookGenre::create(['name' => 'Test']))
                ->toThrow(Exception::class, 'ChinookGenre is a compatibility layer');
        });
    });

    describe('Artist Taxonomy Integration', function () {
        it('can attach genres to artists', function () {
            $artist = ChinookArtist::create([
                'name' => 'Test Artist',
                'biography' => 'A test artist for taxonomy integration',
            ]);

            $rockTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Rock')
                ->first();

            $artist->attachGenre($rockTaxonomy->id, true);

            expect($artist->genres()->count())->toBe(1);
            expect($artist->primaryGenre()->name)->toBe('Rock');
        });

        it('can query artists by genre', function () {
            $artist = ChinookArtist::create([
                'name' => 'Rock Artist',
                'biography' => 'A rock artist',
            ]);

            $rockTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Rock')
                ->first();

            $artist->attachGenre($rockTaxonomy->id, true);

            $rockArtists = ChinookArtist::byGenre('rock')->get();
            expect($rockArtists)->toHaveCount(1);
            expect($rockArtists->first()->name)->toBe('Rock Artist');
        });

        it('supports multiple genres per artist', function () {
            $artist = ChinookArtist::create([
                'name' => 'Multi-Genre Artist',
                'biography' => 'An artist with multiple genres',
            ]);

            $rockTaxonomy = Taxonomy::where('type', 'genre')->where('name', 'Rock')->first();
            $jazzTaxonomy = Taxonomy::where('type', 'genre')->where('name', 'Jazz')->first();

            $artist->attachGenre($rockTaxonomy->id, true);
            $artist->attachGenre($jazzTaxonomy->id, false);

            expect($artist->genres()->count())->toBe(2);
            expect($artist->primaryGenre()->name)->toBe('Rock');
        });
    });

    describe('Album Taxonomy Integration', function () {
        it('can attach genres to albums', function () {
            $artist = ChinookArtist::create(['name' => 'Test Artist']);
            $album = ChinookAlbum::create([
                'title' => 'Test Album',
                'artist_id' => $artist->id,
            ]);

            $popTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Pop')
                ->first();

            $album->attachGenre($popTaxonomy->id, true);

            expect($album->genres()->count())->toBe(1);
            expect($album->primaryGenre()->name)->toBe('Pop');
        });

        it('inherits genres from artist when created', function () {
            $artist = ChinookArtist::create(['name' => 'Jazz Artist']);
            
            $jazzTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Jazz')
                ->first();
            
            $artist->attachGenre($jazzTaxonomy->id, true);

            $album = ChinookAlbum::create([
                'title' => 'Jazz Album',
                'artist_id' => $artist->id,
            ]);

            // Manually trigger inheritance since we're in a test
            $album->inheritArtistGenres();

            expect($album->genres()->count())->toBe(1);
            expect($album->genres()->first()->name)->toBe('Jazz');
        });

        it('can query albums by genre', function () {
            $artist = ChinookArtist::create(['name' => 'Blues Artist']);
            $album = ChinookAlbum::create([
                'title' => 'Blues Album',
                'artist_id' => $artist->id,
            ]);

            $bluesTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Blues')
                ->first();

            $album->attachGenre($bluesTaxonomy->id, true);

            $bluesAlbums = ChinookAlbum::byGenre('blues')->get();
            expect($bluesAlbums)->toHaveCount(1);
            expect($bluesAlbums->first()->title)->toBe('Blues Album');
        });
    });

    describe('Track Taxonomy Integration', function () {
        it('can attach genres to tracks', function () {
            $artist = ChinookArtist::create(['name' => 'Test Artist']);
            $album = ChinookAlbum::create([
                'title' => 'Test Album',
                'artist_id' => $artist->id,
            ]);
            $track = ChinookTrack::create([
                'name' => 'Test Track',
                'album_id' => $album->id,
                'milliseconds' => 180000,
                'unit_price' => 0.99,
            ]);

            $metalTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Metal')
                ->first();

            $track->attachGenre($metalTaxonomy->id, true);

            expect($track->genres()->count())->toBe(1);
            expect($track->primaryGenre()->name)->toBe('Metal');
        });

        it('inherits genres from album when created', function () {
            $artist = ChinookArtist::create(['name' => 'Classical Artist']);
            $album = ChinookAlbum::create([
                'title' => 'Classical Album',
                'artist_id' => $artist->id,
            ]);

            $classicalTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Classical')
                ->first();
            
            $album->attachGenre($classicalTaxonomy->id, true);

            $track = ChinookTrack::create([
                'name' => 'Classical Track',
                'album_id' => $album->id,
                'milliseconds' => 240000,
                'unit_price' => 1.29,
            ]);

            // Manually trigger inheritance since we're in a test
            $track->inheritAlbumGenres();

            expect($track->genres()->count())->toBe(1);
            expect($track->genres()->first()->name)->toBe('Classical');
        });

        it('supports multiple taxonomy types for tracks', function () {
            $artist = ChinookArtist::create(['name' => 'Electronic Artist']);
            $album = ChinookAlbum::create([
                'title' => 'Electronic Album',
                'artist_id' => $artist->id,
            ]);
            $track = ChinookTrack::create([
                'name' => 'Electronic Track',
                'album_id' => $album->id,
                'milliseconds' => 200000,
                'unit_price' => 0.99,
            ]);

            $electronicaTaxonomy = Taxonomy::where('type', 'genre')
                ->where('name', 'Electronica/Dance')
                ->first();

            $track->attachGenre($electronicaTaxonomy->id, true);

            // Test that track supports multiple taxonomy types
            $supportedTypes = $track->getTaxonomyTypes();
            expect($supportedTypes)->toContain('genre', 'mood', 'instrument', 'tempo');
        });
    });
});
