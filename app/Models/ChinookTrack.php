<?php

namespace App\Models;

use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

/**
 * ChinookTrack Model
 * 
 * Represents tracks in the Chinook music database with taxonomy support
 * for categorization by genre, mood, instrument, and other musical attributes.
 * 
 * @property int $id
 * @property string $public_id
 * @property string $name
 * @property string $slug
 * @property int $album_id
 * @property int|null $media_type_id
 * @property string|null $composer
 * @property int $milliseconds
 * @property int|null $bytes
 * @property float $unit_price
 * @property array|null $metadata
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 */
class ChinookTrack extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTags;
    use HasSlug;
    use HasTaxonomies;

    protected $table = 'chinook_tracks';

    protected $fillable = [
        'public_id',
        'name',
        'slug',
        'album_id',
        'media_type_id',
        'composer',
        'milliseconds',
        'bytes',
        'unit_price',
        'metadata',
        'is_active',
    ];

    /**
     * Laravel 12 modern cast() method
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'name' => 'string',
            'slug' => 'string',
            'album_id' => 'integer',
            'media_type_id' => 'integer',
            'composer' => 'string',
            'milliseconds' => 'integer',
            'bytes' => 'integer',
            'unit_price' => 'decimal:2',
            'metadata' => 'array',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Album relationship
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(ChinookAlbum::class, 'album_id');
    }

    /**
     * Artist relationship (through album)
     */
    public function artist()
    {
        return $this->hasOneThrough(
            ChinookArtist::class,
            ChinookAlbum::class,
            'id',
            'id',
            'album_id',
            'artist_id'
        );
    }

    /**
     * Configure supported taxonomy types for tracks
     */
    public function getTaxonomyTypes(): array
    {
        return [
            'genre',
            'mood',
            'theme',
            'instrument',
            'era',
            'language',
            'occasion',
            'tempo',
        ];
    }

    /**
     * Get primary genre taxonomy
     */
    public function primaryGenre()
    {
        return $this->taxonomies()
            ->where('type', 'genre')
            ->wherePivot('meta->is_primary', true)
            ->first();
    }

    /**
     * Get all genre taxonomies
     */
    public function genres()
    {
        return $this->taxonomies()->where('type', 'genre');
    }

    /**
     * Get mood taxonomies
     */
    public function moods()
    {
        return $this->taxonomies()->where('type', 'mood');
    }

    /**
     * Get instrument taxonomies
     */
    public function instruments()
    {
        return $this->taxonomies()->where('type', 'instrument');
    }

    /**
     * Attach genre taxonomy with metadata
     */
    public function attachGenre(int $taxonomyId, bool $isPrimary = false, array $metadata = []): void
    {
        $this->attachTaxonomy($taxonomyId, array_merge($metadata, [
            'is_primary' => $isPrimary,
            'source' => 'chinook_import',
        ]));
    }

    /**
     * Inherit genres from album if not set
     */
    public function inheritAlbumGenres(): void
    {
        if ($this->genres()->count() === 0 && $this->album) {
            $albumGenres = $this->album->genres()->get();
            
            foreach ($albumGenres as $genre) {
                $this->attachGenre($genre->id, false, [
                    'inherited_from' => 'album',
                    'album_id' => $this->album_id,
                ]);
            }
        }
    }

    /**
     * Scope for active tracks
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for tracks by genre
     */
    public function scopeByGenre($query, string $genreSlug)
    {
        return $query->whereHas('taxonomies', function ($q) use ($genreSlug) {
            $q->where('type', 'genre')
              ->where('slug', $genreSlug);
        });
    }

    /**
     * Scope for tracks by album
     */
    public function scopeByAlbum($query, int $albumId)
    {
        return $query->where('album_id', $albumId);
    }

    /**
     * Scope for tracks by duration range
     */
    public function scopeByDuration($query, int $minMs = null, int $maxMs = null)
    {
        if ($minMs) {
            $query->where('milliseconds', '>=', $minMs);
        }
        if ($maxMs) {
            $query->where('milliseconds', '<=', $maxMs);
        }
        return $query;
    }

    /**
     * Get track's primary genre name for compatibility
     */
    public function getGenreNameAttribute(): ?string
    {
        return $this->primaryGenre()?->name;
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $seconds = intval($this->milliseconds / 1000);
        $minutes = intval($seconds / 60);
        $seconds = $seconds % 60;
        
        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get file size in human readable format
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->bytes) {
            return 'Unknown';
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->bytes;
        $unit = 0;
        
        while ($bytes >= 1024 && $unit < count($units) - 1) {
            $bytes /= 1024;
            $unit++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unit];
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($track) {
            if (empty($track->public_id)) {
                $track->public_id = 'TRK-' . strtoupper(uniqid());
            }
            if (!isset($track->is_active)) {
                $track->is_active = true;
            }
        });

        static::created(function ($track) {
            // Auto-inherit genres from album after creation
            $track->inheritAlbumGenres();
        });
    }
}
