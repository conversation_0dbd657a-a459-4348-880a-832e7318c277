<?php

namespace App\Models;

use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

/**
 * ChinookArtist Model
 * 
 * Represents artists in the Chinook music database with taxonomy support
 * for categorization by genre, style, era, and other musical attributes.
 * 
 * @property int $id
 * @property string $public_id
 * @property string $name
 * @property string $slug
 * @property string|null $biography
 * @property array|null $metadata
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 */
class ChinookArtist extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTags;
    use HasSlug;
    use HasTaxonomies;

    protected $table = 'chinook_artists';

    protected $fillable = [
        'public_id',
        'name',
        'slug',
        'biography',
        'metadata',
        'is_active',
    ];

    /**
     * Laravel 12 modern cast() method
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'name' => 'string',
            'slug' => 'string',
            'biography' => 'string',
            'metadata' => 'array',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Albums relationship
     */
    public function albums(): HasMany
    {
        return $this->hasMany(ChinookAlbum::class, 'artist_id');
    }

    /**
     * Tracks relationship (through albums)
     */
    public function tracks(): HasMany
    {
        return $this->hasManyThrough(
            ChinookTrack::class,
            ChinookAlbum::class,
            'artist_id',
            'album_id',
            'id',
            'id'
        );
    }

    /**
     * Configure supported taxonomy types for artists
     */
    public function getTaxonomyTypes(): array
    {
        return [
            'genre',
            'style',
            'era',
            'origin',
            'mood',
            'instrument',
            'language',
        ];
    }

    /**
     * Get primary genre taxonomy
     */
    public function primaryGenre()
    {
        return $this->taxonomies()
            ->where('type', 'genre')
            ->wherePivot('meta->is_primary', true)
            ->first();
    }

    /**
     * Get all genre taxonomies
     */
    public function genres()
    {
        return $this->taxonomies()->where('type', 'genre');
    }

    /**
     * Attach genre taxonomy with metadata
     */
    public function attachGenre(int $taxonomyId, bool $isPrimary = false, array $metadata = []): void
    {
        $this->attachTaxonomy($taxonomyId, array_merge($metadata, [
            'is_primary' => $isPrimary,
            'source' => 'chinook_import',
        ]));
    }

    /**
     * Scope for active artists
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for artists by genre
     */
    public function scopeByGenre($query, string $genreSlug)
    {
        return $query->whereHas('taxonomies', function ($q) use ($genreSlug) {
            $q->where('type', 'genre')
              ->where('slug', $genreSlug);
        });
    }

    /**
     * Get artist's primary genre name for compatibility
     */
    public function getGenreNameAttribute(): ?string
    {
        return $this->primaryGenre()?->name;
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($artist) {
            if (empty($artist->public_id)) {
                $artist->public_id = 'ART-' . strtoupper(uniqid());
            }
            if (!isset($artist->is_active)) {
                $artist->is_active = true;
            }
        });
    }
}
