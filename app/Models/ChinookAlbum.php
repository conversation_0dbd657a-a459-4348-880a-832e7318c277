<?php

namespace App\Models;

use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomies;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

/**
 * ChinookAlbum Model
 * 
 * Represents albums in the Chinook music database with taxonomy support
 * for categorization by genre, theme, era, and other musical attributes.
 * 
 * @property int $id
 * @property string $public_id
 * @property string $title
 * @property string $slug
 * @property int $artist_id
 * @property string|null $release_date
 * @property string|null $description
 * @property array|null $metadata
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 */
class ChinookAlbum extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTags;
    use HasSlug;
    use HasTaxonomies;

    protected $table = 'chinook_albums';

    protected $fillable = [
        'public_id',
        'title',
        'slug',
        'artist_id',
        'release_date',
        'description',
        'metadata',
        'is_active',
    ];

    /**
     * Laravel 12 modern cast() method
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'title' => 'string',
            'slug' => 'string',
            'artist_id' => 'integer',
            'release_date' => 'date',
            'description' => 'string',
            'metadata' => 'array',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Artist relationship
     */
    public function artist(): BelongsTo
    {
        return $this->belongsTo(ChinookArtist::class, 'artist_id');
    }

    /**
     * Tracks relationship
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(ChinookTrack::class, 'album_id');
    }

    /**
     * Configure supported taxonomy types for albums
     */
    public function getTaxonomyTypes(): array
    {
        return [
            'genre',
            'theme',
            'era',
            'mood',
            'occasion',
            'language',
            'style',
        ];
    }

    /**
     * Get primary genre taxonomy
     */
    public function primaryGenre()
    {
        return $this->taxonomies()
            ->where('type', 'genre')
            ->wherePivot('meta->is_primary', true)
            ->first();
    }

    /**
     * Get all genre taxonomies
     */
    public function genres()
    {
        return $this->taxonomies()->where('type', 'genre');
    }

    /**
     * Get theme taxonomies
     */
    public function themes()
    {
        return $this->taxonomies()->where('type', 'theme');
    }

    /**
     * Attach genre taxonomy with metadata
     */
    public function attachGenre(int $taxonomyId, bool $isPrimary = false, array $metadata = []): void
    {
        $this->attachTaxonomy($taxonomyId, array_merge($metadata, [
            'is_primary' => $isPrimary,
            'source' => 'chinook_import',
        ]));
    }

    /**
     * Inherit genres from artist if not set
     */
    public function inheritArtistGenres(): void
    {
        if ($this->genres()->count() === 0 && $this->artist) {
            $artistGenres = $this->artist->genres()->get();
            
            foreach ($artistGenres as $genre) {
                $this->attachGenre($genre->id, false, [
                    'inherited_from' => 'artist',
                    'artist_id' => $this->artist_id,
                ]);
            }
        }
    }

    /**
     * Scope for active albums
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for albums by genre
     */
    public function scopeByGenre($query, string $genreSlug)
    {
        return $query->whereHas('taxonomies', function ($q) use ($genreSlug) {
            $q->where('type', 'genre')
              ->where('slug', $genreSlug);
        });
    }

    /**
     * Scope for albums by artist
     */
    public function scopeByArtist($query, int $artistId)
    {
        return $query->where('artist_id', $artistId);
    }

    /**
     * Get album's primary genre name for compatibility
     */
    public function getGenreNameAttribute(): ?string
    {
        return $this->primaryGenre()?->name;
    }

    /**
     * Get total track count
     */
    public function getTrackCountAttribute(): int
    {
        return $this->tracks()->count();
    }

    /**
     * Get total duration in milliseconds
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->tracks()->sum('milliseconds');
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($album) {
            if (empty($album->public_id)) {
                $album->public_id = 'ALB-' . strtoupper(uniqid());
            }
            if (!isset($album->is_active)) {
                $album->is_active = true;
            }
        });

        static::created(function ($album) {
            // Auto-inherit genres from artist after creation
            $album->inheritArtistGenres();
        });
    }
}
