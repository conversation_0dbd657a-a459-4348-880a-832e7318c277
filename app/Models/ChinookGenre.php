<?php

namespace App\Models;

use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * ChinookGenre Compatibility Layer Model
 * 
 * This model provides backward compatibility with the original Chinook database
 * schema while leveraging the modern taxonomy system underneath. It acts as a
 * materialized view/facade over the taxonomy system for genre-type taxonomies.
 * 
 * This enables seamless import/export with the original chinook.sql format
 * while using the advanced taxonomy features internally.
 * 
 * @property int $id - Maps to taxonomy.id where type='genre'
 * @property string $name - Maps to taxonomy.name
 * @property string $slug - Maps to taxonomy.slug
 * @property string|null $description - Maps to taxonomy.description
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ChinookGenre extends Model
{
    use HasFactory;

    protected $table = 'chinook_genres';

    protected $fillable = [
        'id',
        'name',
        'slug',
        'description',
    ];

    /**
     * Laravel 12 modern cast() method
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'name' => 'string',
            'slug' => 'string',
            'description' => 'string',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Disable auto-incrementing since we map to taxonomy IDs
     */
    public $incrementing = false;

    /**
     * Get the underlying taxonomy record
     */
    public function taxonomy()
    {
        return $this->belongsTo(Taxonomy::class, 'id', 'id')
            ->where('type', 'genre');
    }

    /**
     * Artists relationship (through taxonomy system)
     */
    public function artists(): HasManyThrough
    {
        return $this->hasManyThrough(
            ChinookArtist::class,
            'taxonomables',
            'taxonomy_id',
            'id',
            'id',
            'taxonomable_id'
        )->where('taxonomables.taxonomable_type', ChinookArtist::class)
         ->where('taxonomies.type', 'genre');
    }

    /**
     * Albums relationship (through taxonomy system)
     */
    public function albums(): HasManyThrough
    {
        return $this->hasManyThrough(
            ChinookAlbum::class,
            'taxonomables',
            'taxonomy_id',
            'id',
            'id',
            'taxonomable_id'
        )->where('taxonomables.taxonomable_type', ChinookAlbum::class)
         ->where('taxonomies.type', 'genre');
    }

    /**
     * Tracks relationship (through taxonomy system)
     */
    public function tracks(): HasManyThrough
    {
        return $this->hasManyThrough(
            ChinookTrack::class,
            'taxonomables',
            'taxonomy_id',
            'id',
            'id',
            'taxonomable_id'
        )->where('taxonomables.taxonomable_type', ChinookTrack::class)
         ->where('taxonomies.type', 'genre');
    }

    /**
     * Create a new ChinookGenre from taxonomy data
     */
    public static function createFromTaxonomy(Taxonomy $taxonomy): self
    {
        if ($taxonomy->type !== 'genre') {
            throw new \InvalidArgumentException('Taxonomy must be of type "genre"');
        }

        $genre = new static();
        $genre->id = $taxonomy->id;
        $genre->name = $taxonomy->name;
        $genre->slug = $taxonomy->slug;
        $genre->description = $taxonomy->description;
        $genre->created_at = $taxonomy->created_at;
        $genre->updated_at = $taxonomy->updated_at;

        return $genre;
    }

    /**
     * Sync with underlying taxonomy
     */
    public function syncWithTaxonomy(): void
    {
        $taxonomy = $this->taxonomy;
        if ($taxonomy) {
            $this->name = $taxonomy->name;
            $this->slug = $taxonomy->slug;
            $this->description = $taxonomy->description;
            $this->updated_at = $taxonomy->updated_at;
        }
    }

    /**
     * Get all genres as ChinookGenre instances
     */
    public static function allGenres()
    {
        return Taxonomy::where('type', 'genre')
            ->get()
            ->map(function ($taxonomy) {
                return static::createFromTaxonomy($taxonomy);
            });
    }

    /**
     * Find genre by original Chinook ID
     */
    public static function findByChinookId(int $chinookId): ?self
    {
        $taxonomy = Taxonomy::where('type', 'genre')
            ->where('meta->chinook_id', $chinookId)
            ->first();

        return $taxonomy ? static::createFromTaxonomy($taxonomy) : null;
    }

    /**
     * Find genre by name (case-insensitive)
     */
    public static function findByName(string $name): ?self
    {
        $taxonomy = Taxonomy::where('type', 'genre')
            ->whereRaw('LOWER(name) = ?', [strtolower($name)])
            ->first();

        return $taxonomy ? static::createFromTaxonomy($taxonomy) : null;
    }

    /**
     * Create genre in taxonomy system
     */
    public static function createGenre(array $attributes): self
    {
        $taxonomy = Taxonomy::create([
            'name' => $attributes['name'],
            'slug' => $attributes['slug'] ?? null,
            'type' => 'genre',
            'description' => $attributes['description'] ?? null,
            'meta' => [
                'chinook_id' => $attributes['chinook_id'] ?? null,
                'source' => 'chinook_import',
                'created_via' => 'compatibility_layer',
            ],
        ]);

        return static::createFromTaxonomy($taxonomy);
    }

    /**
     * Export to original Chinook format
     */
    public function toChinookArray(): array
    {
        return [
            'id' => $this->taxonomy?->meta['chinook_id'] ?? $this->id,
            'name' => $this->name,
        ];
    }

    /**
     * Export all genres to Chinook format
     */
    public static function exportToChinookFormat(): array
    {
        return static::allGenres()
            ->map(fn($genre) => $genre->toChinookArray())
            ->toArray();
    }

    /**
     * Import from original Chinook format
     */
    public static function importFromChinookData(array $chinookGenres): void
    {
        foreach ($chinookGenres as $genreData) {
            $existing = static::findByChinookId($genreData['id']);
            
            if (!$existing) {
                static::createGenre([
                    'name' => $genreData['name'],
                    'chinook_id' => $genreData['id'],
                    'description' => "Imported from original Chinook database",
                ]);
            }
        }
    }

    /**
     * Get statistics for this genre
     */
    public function getStats(): array
    {
        return [
            'artists_count' => $this->artists()->count(),
            'albums_count' => $this->albums()->count(),
            'tracks_count' => $this->tracks()->count(),
        ];
    }

    /**
     * Scope for active genres (with content)
     */
    public function scopeWithContent($query)
    {
        return $query->whereHas('tracks');
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        // Prevent direct database operations - this is a facade
        static::creating(function () {
            throw new \Exception('ChinookGenre is a compatibility layer. Use createGenre() method instead.');
        });

        static::updating(function () {
            throw new \Exception('ChinookGenre is a compatibility layer. Update the underlying taxonomy instead.');
        });

        static::deleting(function () {
            throw new \Exception('ChinookGenre is a compatibility layer. Delete the underlying taxonomy instead.');
        });
    }
}
