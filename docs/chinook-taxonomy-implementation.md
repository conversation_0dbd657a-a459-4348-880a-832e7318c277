# Chinook Taxonomy Implementation Guide

## 1. Overview

This guide covers the **greenfield implementation** of the Chinook music database using Laravel 12 with the `aliziodev/laravel-taxonomy` package as the sole categorization system. This approach provides a clean, modern architecture while maintaining compatibility with the original Chinook database schema.

## 2. Architecture

### 2.1 Single Taxonomy System

The implementation uses **only** the `aliziodev/laravel-taxonomy` package for all categorization needs:

- **Primary System**: All genre categorization through taxonomy system
- **No Dual Systems**: No custom Category models or CategoryType enums
- **Clean Architecture**: Single source of truth for all taxonomic data
- **Modern Laravel**: Full Laravel 12 compliance with current best practices

### 2.2 Compatibility Layer

The `ChinookGenre` model provides backward compatibility:

- **Materialized View**: Acts as a facade over the taxonomy system
- **Import/Export**: Maintains compatibility with original `chinook.sql` format
- **Data Integrity**: Preserves original Chinook genre IDs and relationships
- **Seamless Integration**: Transparent to existing import/export workflows

## 3. Models

### 3.1 Core Models

All Chinook models use Laravel 12 modern syntax and taxonomy integration:

<augment_code_snippet path="app/Models/ChinookArtist.php" mode="EXCERPT">
````php
class ChinookArtist extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasTags;
    use HasSlug;
    use HasTaxonomies;  // Single taxonomy system

    protected function casts(): array  // Laravel 12 modern syntax
    {
        return [
            'metadata' => 'array',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
        ];
    }
````
</augment_code_snippet>

### 3.2 Taxonomy Integration

Each model supports specific taxonomy types:

- **Artists**: genre, style, era, origin, mood, instrument, language
- **Albums**: genre, theme, era, mood, occasion, language, style  
- **Tracks**: genre, mood, theme, instrument, era, language, occasion, tempo

### 3.3 Compatibility Layer

<augment_code_snippet path="app/Models/ChinookGenre.php" mode="EXCERPT">
````php
class ChinookGenre extends Model
{
    // Provides backward compatibility with original Chinook schema
    public static function findByChinookId(int $chinookId): ?self
    public function toChinookArray(): array
    public static function exportToChinookFormat(): array
    public static function importFromChinookData(array $chinookGenres): void
````
</augment_code_snippet>

## 4. Database Schema

### 4.1 Modern Laravel Tables

All tables follow Laravel conventions with modern features:

- **Timestamps**: `created_at`, `updated_at`, `deleted_at`
- **User Stamps**: `created_by`, `updated_by`, `deleted_by`
- **Public IDs**: Unique identifiers for external references
- **Slugs**: SEO-friendly URLs
- **JSON Metadata**: Flexible additional data storage
- **Full-Text Search**: Optimized search capabilities

### 4.2 Compatibility View

The `chinook_genres` view provides seamless compatibility:

```sql
CREATE VIEW chinook_genres AS
SELECT 
    t.id,
    t.name,
    t.slug,
    t.description,
    JSON_UNQUOTE(JSON_EXTRACT(t.meta, '$.chinook_id')) as chinook_id
FROM taxonomies t
WHERE t.type = 'genre'
```

## 5. Implementation Steps

### 5.1 Installation

The taxonomy package is already installed and configured:

```bash
# Already completed
composer require aliziodev/laravel-taxonomy
php artisan vendor:publish --provider="Aliziodev\LaravelTaxonomy\TaxonomyServiceProvider"
```

### 5.2 Database Setup

Run migrations and seed the taxonomy system:

```bash
php artisan migrate
php artisan db:seed --class=ChinookGenreTaxonomySeeder
```

### 5.3 Verification

Verify the implementation:

```bash
php artisan tinker
>>> App\Models\ChinookGenre::allGenres()->count()
=> 25
>>> Aliziodev\LaravelTaxonomy\Models\Taxonomy::where('type', 'genre')->count()
=> 25
```

## 6. Usage Examples

### 6.1 Creating Artists with Genres

```php
use App\Models\ChinookArtist;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;

$artist = ChinookArtist::create([
    'name' => 'Led Zeppelin',
    'biography' => 'Legendary rock band',
]);

$rockGenre = Taxonomy::where('type', 'genre')
    ->where('name', 'Rock')
    ->first();

$artist->attachGenre($rockGenre->id, true); // true = primary genre
```

### 6.2 Querying by Genre

```php
// Find all rock artists
$rockArtists = ChinookArtist::byGenre('rock')->get();

// Find all jazz albums
$jazzAlbums = ChinookAlbum::byGenre('jazz')->get();

// Find all classical tracks
$classicalTracks = ChinookTrack::byGenre('classical')->get();
```

### 6.3 Compatibility Layer Usage

```php
use App\Models\ChinookGenre;

// Find by original Chinook ID
$rockGenre = ChinookGenre::findByChinookId(1);

// Export to original format
$chinookData = ChinookGenre::exportToChinookFormat();

// Import from original format
ChinookGenre::importFromChinookData($originalData);
```

## 7. Testing

### 7.1 Comprehensive Test Suite

The implementation includes comprehensive Pest PHP tests:

- **Integration Tests**: Full taxonomy system integration
- **Unit Tests**: Individual component functionality
- **Compatibility Tests**: Backward compatibility verification
- **Performance Tests**: Query optimization validation

### 7.2 Running Tests

```bash
php artisan test --filter=ChinookTaxonomy
php artisan test tests/Unit/ChinookGenreCompatibilityTest.php
php artisan test tests/Feature/ChinookTaxonomyIntegrationTest.php
```

## 8. Performance Optimization

### 8.1 Database Indexes

Optimized indexes for common queries:

- **Taxonomy Lookups**: `type + name`, `type + slug`
- **Polymorphic Relations**: `taxonomy_id + taxonomable_type`
- **Genre Queries**: JSON index on `meta->chinook_id`

### 8.2 Eager Loading

Prevent N+1 queries with proper eager loading:

```php
// Load artists with their genres
$artists = ChinookArtist::with('taxonomies')->get();

// Load tracks with album and artist genres
$tracks = ChinookTrack::with([
    'album.artist.taxonomies',
    'taxonomies'
])->get();
```

## 9. Migration from Original Data

### 9.1 Data Import Process

1. **Seed Genres**: Run `ChinookGenreTaxonomySeeder`
2. **Import Artists**: Create artists and attach genres
3. **Import Albums**: Create albums with inherited genres
4. **Import Tracks**: Create tracks with inherited genres

### 9.2 Data Integrity

- **Referential Integrity**: Foreign key constraints ensure data consistency
- **Taxonomy Validation**: Only valid taxonomy types are allowed
- **Compatibility Mapping**: Original Chinook IDs preserved in metadata

## 10. Best Practices

### 10.1 Model Design

- **Single Responsibility**: Each model handles its specific domain
- **Trait Composition**: Consistent trait usage across all models
- **Modern Syntax**: Laravel 12 `cast()` method and current patterns
- **Type Safety**: Proper type hints and return types

### 10.2 Taxonomy Usage

- **Consistent Types**: Use predefined taxonomy types
- **Metadata Storage**: Store additional context in pivot metadata
- **Performance**: Use appropriate indexes and eager loading
- **Compatibility**: Maintain original Chinook ID mapping

## 11. Troubleshooting

### 11.1 Common Issues

**Genre Not Found**: Ensure taxonomy seeder has run successfully
```bash
php artisan db:seed --class=ChinookGenreTaxonomySeeder
```

**Relationship Errors**: Verify foreign key constraints and model relationships
```php
// Check if taxonomy exists
$taxonomy = Taxonomy::find($taxonomyId);
if (!$taxonomy) {
    throw new Exception("Taxonomy not found: {$taxonomyId}");
}
```

**Performance Issues**: Use proper eager loading and indexes
```php
// Good: Eager load relationships
$artists = ChinookArtist::with('taxonomies')->get();

// Bad: N+1 query problem
$artists = ChinookArtist::all();
foreach ($artists as $artist) {
    echo $artist->taxonomies->count(); // N+1 queries
}
```

### 11.2 Debugging

Enable query logging to identify performance issues:

```php
DB::enableQueryLog();
// Your queries here
dd(DB::getQueryLog());
```

## 12. Conclusion

This greenfield implementation provides:

- **Clean Architecture**: Single taxonomy system with no legacy complexity
- **Modern Laravel**: Full Laravel 12 compliance and best practices
- **Backward Compatibility**: Seamless integration with original Chinook data
- **Performance**: Optimized queries and proper indexing
- **Maintainability**: Clear separation of concerns and comprehensive testing

The implementation successfully bridges modern Laravel development practices with the classic Chinook database structure, providing a robust foundation for music catalog management.
